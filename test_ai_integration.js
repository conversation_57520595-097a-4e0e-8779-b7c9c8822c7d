require('dotenv').config();

const { GoogleGenerativeAI } = require('@google/generative-ai');

async function testAIIntegration() {
    console.log('🧪 Testing AI Integration...\n');
    
    // Check API key
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
        console.error('❌ GEMINI_API_KEY not found in environment variables');
        console.log('💡 Please create a .env file with: GEMINI_API_KEY=your_api_key_here');
        return;
    }
    
    console.log('✅ API Key found');
    
    try {
        // Initialize AI
        const genAI = new GoogleGenerativeAI(apiKey);
        const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });
        
        console.log('✅ AI Model initialized');
        
        // Test basic AI response
        console.log('\n🤖 Testing basic AI response...');
        const testPrompt = 'You are an ASM expert. Briefly explain what Attack Surface Management is.';
        
        const result = await model.generateContent(testPrompt);
        const response = await result.response;
        const text = response.text();
        
        console.log('✅ AI Response received:');
        console.log('📝 Response:', text.substring(0, 200) + '...');
        
        // Test ASM-specific prompt
        console.log('\n🎯 Testing ASM-specific prompt...');
        const asmPrompt = `You are an AI ASM system. For target "example.com", what would be your first reconnaissance command? Respond with:
        
ANALYSIS: Brief analysis
RISK_LEVEL: LOW/MEDIUM/HIGH
NEXT_ACTION: What to do next
COMMAND: Single bash command in code block`;
        
        const asmResult = await model.generateContent(asmPrompt);
        const asmResponse = await asmResult.response;
        const asmText = asmResponse.text();
        
        console.log('✅ ASM-specific response received:');
        console.log('📝 Response:', asmText);
        
        // Test command parsing
        console.log('\n🔍 Testing command parsing...');
        const commandMatch = asmText.match(/```bash\n([\s\S]*?)\n```/);
        if (commandMatch) {
            console.log('✅ Command found:', commandMatch[1].trim());
        } else {
            console.log('⚠️  No command found in response');
        }
        
        console.log('\n🎉 AI Integration Test Complete!');
        console.log('✅ All tests passed - Your AI system is ready to use');
        
    } catch (error) {
        console.error('❌ AI Integration Test Failed:');
        console.error('Error:', error.message);
        
        if (error.message.includes('API_KEY_INVALID')) {
            console.log('\n💡 Troubleshooting:');
            console.log('1. Check if your API key is correct');
            console.log('2. Ensure the API key has proper permissions');
            console.log('3. Verify your Google AI Studio account is active');
        }
    }
}

// Run the test
testAIIntegration();
